# Redis Pub/Sub 与缓存机制设计

## 目录
- [1 概述](#1-概述)
  - [1.1 核心架构](#11-核心架构)
  - [1.2 设计目标](#12-设计目标)
- [2 核心组件详解](#2-核心组件详解)
  - [2.1 ChatEventBus (统一事件总线)](#21-chateventbus-统一事件总线)
  - [2.2 AsynchronousRequestProcessor (异步请求处理器)](#22-asynchronousrequestprocessor-异步请求处理器)
  - [2.3 RedisPubSubStream (Pub/Sub流包装器)](#23-redispubsubstream-pubsub流包装器)
- [3 双轨制架构设计](#3-双轨制架构设计)
  - [3.1 实时轨道 (Pub/Sub)](#31-实时轨道-pubsub)
  - [3.2 回放轨道 (Cache)](#32-回放轨道-cache)
  - [3.3 智能降级机制](#33-智能降级机制)
- [4 核心处理流程](#4-核心处理流程)
  - [4.1 详细处理步骤](#41-详细处理步骤)
- [5 可靠性保障](#5-可靠性保障)
- [6 故障排查](#6-故障排查)

## 1 概述

本文档详细阐述 ECS 深度诊断系统中基于 Redis 的双轨制通信与缓存架构。该架构通过 Pub/Sub 实时推送和快照缓存回退的组合，在保证低延迟的同时确保了系统的高可靠性和最终一致性。

### 1.1 核心架构

![Redis Pub/Sub 与快照缓存双轨制架构](../../assets/png/18_redis_pubsub_and_cache.png)

<div align="center"><em>图1-1: Redis Pub/Sub 与快照缓存双轨制架构 - 展示了低延迟实时推送与高可靠缓存回退的完整设计</em></div>

系统采用双轨制设计模式：

**实时轨道 (Pub/Sub) + 回放轨道 (Cache)**

1.  **实时轨道**: 利用 Redis Pub/Sub 提供毫秒级事件推送，确保前端实时接收 Agent 执行过程。
2.  **回放轨道**: 利用 Redis 缓存存储事件快照，为 Pub/Sub 失败时提供可靠回退机制。
3.  **智能降级**: 消费端自动在两种模式间切换，对上层业务完全透明。

### 1.2 设计目标

*   **低延迟与高可靠兼得**: 通过双轨制架构同时满足实时性和可靠性需求。
*   **智能降级**: 自动故障检测和无缝切换，确保服务连续性。
*   **统一抽象**: ChatEventBus 封装所有 Redis 操作，简化上层业务逻辑。
*   **可扩展性**: 支持多订阅者和水平扩展。

---

## 2 核心组件详解

### 2.1 ChatEventBus (统一事件总线)

这是整个双轨制架构的核心抽象层，封装了所有与 Redis 的交互。

*   **职责**:
    *   **统一命名**: 根据 `request_id` 生成一致的 channel 和 key 命名规范。
    *   **双轨发布**: 同时向 Pub/Sub 频道和缓存键写入事件数据。
    *   **订阅管理**: 提供统一的订阅接口，返回可异步迭代的流对象。
    *   **快照管理**: 处理事件快照的读写，包括 TTL 设置。

*   **核心方法**:
    ```python
    async def publish(self, request_id: str, seq: int, event: BaseAgentOutputEvent)
    async def write_snapshot(self, request_id: str, seq: int, event: BaseAgentOutputEvent)
    async def read_snapshot(self, request_id: str) -> Optional[str]
    async def subscribe(self, request_id: str) -> RedisPubSubStream
    ```

*   **命名规范**:
    - **Channel**: `CHAT_EVENT:channel:{request_id}`
    - **Key**: `CHAT_EVENT:{request_id}`

### 2.2 AsynchronousRequestProcessor (异步请求处理器)

负责处理长时间运行的 Agent 请求，实现双轨制消费逻辑。

*   **核心流程**:
    1. **预订阅**: 在启动后台任务前立即订阅 Pub/Sub 频道，避免丢失早期事件。
    2. **后台执行**: 通过 `asyncio.create_task` 启动 Agent 执行任务。
    3. **智能消费**: 优先从 Pub/Sub 流读取，异常时自动降级到轮询模式。
    4. **有序保证**: 通过 `seq` 序列号和缓冲机制确保事件有序传递。

*   **关键特性**:
    - **非阻塞**: 前端立即返回异步生成器，不等待 Agent 完成。
    - **容错性**: 自动处理网络异常、连接断开等故障场景。
    - **性能优化**: 节流写入快照，避免高频写入影响性能。

### 2.3 RedisPubSubStream (Pub/Sub流包装器)

对 Redis Pub/Sub 的轻量级异步包装，提供统一的流式接口。

*   **职责**:
    - 管理 Pub/Sub 连接的生命周期
    - 提供异步迭代器接口
    - 处理连接异常和资源清理

---

## 3 双轨制架构设计

### 3.1 实时轨道 (Pub/Sub)

**优势**: 毫秒级延迟，实时性极佳
**适用场景**: 正常网络条件下的实时事件推送

*   **发布端**: 后台 Agent 任务通过 `ChatEventBus.publish()` 实时发布事件
*   **消费端**: 前端通过 `_stream_via_pubsub()` 订阅并消费事件流
*   **有序保证**: 使用 `EventEnvelope` 包装事件，包含 `seq` 序列号
*   **乱序处理**: 消费端维护缓冲区，确保按序号顺序处理事件

### 3.2 回放轨道 (Cache)

**优势**: 高可靠性，数据持久化
**适用场景**: Pub/Sub 异常时的回退机制和历史查询

*   **存储策略**: 节流写入（如每10条事件写入一次），避免性能影响
*   **TTL设置**: 6小时过期时间，平衡存储成本和可用性
*   **数据格式**: 与 Pub/Sub 相同的 `EventEnvelope` 格式，确保一致性

### 3.3 智能降级机制

系统自动在两种模式间切换，确保服务连续性：

1. **优先模式**: 尝试订阅 Pub/Sub 频道
2. **异常检测**: 监控订阅失败、消息超时等异常
3. **自动切换**: 异常时无缝切换到轮询缓存模式
4. **透明性**: 上层业务无需感知切换过程

---

## 4 核心处理流程

### 4.1 详细处理步骤

1.  **请求初始化**: `ChatService` 接收异步请求，创建 `ChatContext`
2.  **处理器选择**: 根据 Agent 类型选择 `AsynchronousRequestProcessor`
3.  **预订阅**: 立即通过 `ChatEventBus` 订阅 Pub/Sub 频道
4.  **后台启动**: 启动 `_run_agent_in_background` 任务
5.  **双轨发布**: 
    - Agent 产生事件 → `ChatEventBus.publish()` → Redis Channel
    - 节流写入 → `ChatEventBus.write_snapshot()` → Redis Cache
6.  **智能消费**:
    - 优先路径: `_stream_via_pubsub()` 从 Pub/Sub 读取
    - 降级路径: `_stream_from_redis()` 轮询缓存
7.  **事件传递**: 通过异步生成器将事件流式传递给客户端

---

## 5 可靠性保障

*   **自动降级**: Pub/Sub 异常时自动切换到缓存轮询，对用户透明
*   **数据保底**: 快照缓存确保即使 Pub/Sub 完全失败，数据也不会丢失
*   **有序交付**: `seq` 序列号和缓冲机制保证事件按正确顺序处理
*   **幂等性**: 通过事件数据比较避免重复处理
*   **超时处理**: 设置合理的超时时间，避免无限等待
*   **资源清理**: 自动清理 Pub/Sub 连接和相关资源

---

## 6 故障排查

### 6.1 常用命令

```bash
# 检查频道活跃度
redis-cli PUBSUB CHANNELS "CHAT_EVENT:channel:*"

# 检查快照键
redis-cli KEYS "CHAT_EVENT:req_*"

# 模拟订阅测试
redis-cli SUBSCRIBE "CHAT_EVENT:channel:<request_id>"

# 检查键的TTL
redis-cli TTL "CHAT_EVENT:<request_id>"
```

### 6.2 常见问题

1. **事件丢失**: 检查 Pub/Sub 订阅时机，确保在后台任务启动前完成订阅
2. **顺序错乱**: 验证 `seq` 序列号的正确性和缓冲区逻辑
3. **性能问题**: 调整快照写入频率，避免过于频繁的缓存操作
4. **内存泄漏**: 确保 Pub/Sub 连接正确关闭，避免连接池耗尽
