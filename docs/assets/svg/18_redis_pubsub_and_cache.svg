<?xml version="1.0" encoding="UTF-8"?>
<svg width="1800" height="1000" viewBox="0 0 1800 1000" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Gradients based on Reference SVG -->
    <linearGradient id="producerGradient" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:#8b5cf6;"/><stop offset="100%" style="stop-color:#7c3aed;"/></linearGradient>
    <linearGradient id="consumerGradient" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:#3b82f6;"/><stop offset="100%" style="stop-color:#1d4ed8;"/></linearGradient>
    <linearGradient id="infraGradient" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:#64748b;"/><stop offset="100%" style="stop-color:#475569;"/></linearGradient>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="0%" y2="100%"><stop offset="0%" style="stop-color:#f8fafc;"/><stop offset="100%" style="stop-color:#f1f5f9;"/></linearGradient>

    <!-- Styles based on Reference SVG -->
    <style>
      .title { font-family: 'Inter', 'Segoe UI', sans-serif; font-size: 32px; font-weight: 700; fill: #1e293b; }
      .subtitle { font-family: 'Inter', 'Segoe UI', sans-serif; font-size: 18px; fill: #64748b; font-weight: 500; }
      .layer-title { font-family: 'Inter', 'Segoe UI', sans-serif; font-size: 22px; font-weight: 600; fill: white; }
      .layer-desc { font-family: 'Inter', 'Segoe UI', sans-serif; font-size: 14px; fill: white; opacity: 0.9; }
      .component-title { font-family: 'Inter', 'Segoe UI', sans-serif; font-size: 16px; font-weight: 600; fill: #1e293b; }
      .component-desc { font-family: 'Inter', 'Segoe UI', sans-serif; font-size: 13px; fill: #475569; }
      .flow-text { font-family: 'Inter', 'Segoe UI', sans-serif; font-size: 13px; fill: #334155; font-weight: 500; }
      .legend-title { font-family: 'Inter', 'Segoe UI', sans-serif; font-size: 20px; font-weight: 600; fill: #1e293b; }
      .legend-subtitle { font-family: 'Inter', 'Segoe UI', sans-serif; font-size: 16px; font-weight: 600; fill: #f59e0b; } /* Orange accent */
      .legend-desc { font-family: 'Inter', 'Segoe UI', sans-serif; font-size: 14px; line-height: 1.5; fill: #475569; }
      .legend-desc-bold { font-weight: 600; fill: #334155; }
      .mono { font-family: 'SF Mono', 'Monaco', monospace; font-size: 12px; fill: #334155; }

      .layer-box { rx: 16; filter: url(#dropShadow); }
      .component-box { fill: rgba(255,255,255,0.8); rx: 10; stroke: rgba(255,255,255,0.9); stroke-width: 1.5; }
      .legend-box { fill: white; rx: 16; stroke: #e2e8f0; stroke-width: 1; filter: url(#subtleShadow); }
      .flow-arrow { stroke: #475569; stroke-width: 2.5; fill: none; marker-end: url(#arrowhead); }
    </style>

    <!-- Markers & Filters based on Reference SVG -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto"><polygon points="0 0, 10 3.5, 0 7" fill="#475569" /></marker>
    <filter id="dropShadow" x="-10%" y="-10%" width="120%" height="130%"><feDropShadow dx="0" dy="5" stdDeviation="10" flood-color="#1e293b" flood-opacity="0.1"/></filter>
    <filter id="subtleShadow" x="-10%" y="-10%" width="120%" height="120%"><feDropShadow dx="0" dy="4" stdDeviation="8" flood-color="#1e293b" flood-opacity="0.06"/></filter>
  </defs>

  <!-- === BACKGROUND === -->
  <rect width="100%" height="100%" fill="url(#bgGradient)"/>

  <!-- === MAIN TITLE === -->
  <text x="900" y="60" text-anchor="middle" class="title">Redis Pub/Sub 与快照缓存双轨制架构</text>
  <text x="900" y="95" text-anchor="middle" class="subtitle">低延迟实时推送与高可靠数据快照的融合实践</text>

  <!-- === LEGEND / CORE ADVANTAGES (Right Panel) === -->
  <g id="legend-panel">
    <rect x="1220" y="150" width="530" height="450" class="legend-box"/>
    <g transform="translate(1250, 180)">
      <text x="20" y="20" class="legend-title">核心优势</text>
      <g transform="translate(0, 70)">
        <text x="20" y="0" class="legend-subtitle">1. 低延迟与高可靠兼得</text>
        <text x="20" y="30" class="legend-desc">
          <tspan x="20" dy="0em"><tspan class="legend-desc-bold">实时轨道 (Pub/Sub):</tspan> 实现毫秒级事件推送，</tspan>
          <tspan x="20" dy="1.5em">保证前端交互的极致实时性。</tspan>
          <tspan x="20" dy="1.8em"><tspan class="legend-desc-bold">回放轨道 (Snapshot):</tspan> 提供数据持久化与回退保障，</tspan>
          <tspan x="20" dy="1.5em">即使在消费失败后也能确保数据最终送达。</tspan>
        </text>
      </g>
      <g transform="translate(0, 250)">
        <text x="20" y="0" class="legend-subtitle">2. 智能降级与统一总线</text>
        <text x="20" y="30" class="legend-desc">
          <tspan x="20" dy="0em"><tspan class="legend-desc-bold">智能消费端:</tspan> 优先订阅实时流，若发生异常或</tspan>
          <tspan x="20" dy="1.5em">连接中断，则无缝切换至轮询快照模式。</tspan>
          <tspan x="20" dy="1.8em"><tspan class="legend-desc-bold">`ChatEventBus` 封装:</tspan> 统一所有Redis操作，</tspan>
          <tspan x="20" dy="1.5em">为上层逻辑提供简洁、一致的调用接口。</tspan>
        </text>
      </g>
    </g>
  </g>

  <!-- === ARCHITECTURE DIAGRAM (Left/Central Area) === -->
  <g id="main-architecture">
    <!-- Producer Module -->
    <g id="producer-layer">
      <rect x="50" y="200" width="350" height="520" fill="url(#producerGradient)" class="layer-box"/>
      <text x="80" y="248" class="layer-title">📤 生产者模块</text>
      <text x="80" y="275" class="layer-desc">AsynchronousRequestProcessor</text>

      <g transform="translate(75, 320)">
        <rect width="300" height="90" class="component-box"/>
        <text x="150" y="35" text-anchor="middle" class="component-title">后台Agent执行</text>
        <text x="150" y="58" text-anchor="middle" class="mono">_run_agent_in_background()</text>
        <text x="150" y="78" text-anchor="middle" class="component-desc">循环产生 BaseAgentOutputEvent</text>
      </g>
      <path d="M 225 410 V 450" class="flow-arrow" style="stroke:white; stroke-width:2;"/>
      <g transform="translate(75, 450)">
        <rect width="300" height="200" class="component-box"/>
        <text x="150" y="35" text-anchor="middle" class="component-title">调用事件总线</text>
        <text x="20" y="65" class="component-desc" style="font-weight:600; fill:#581c87">实时轨道 (Pub/Sub):</text>
        <text x="35" y="88" class="mono">event_bus.publish(event)</text>
        <text x="35" y="106" class="component-desc">每条事件都实时发布</text>
        <text x="20" y="136" class="component-desc" style="font-weight:600; fill:#581c87">回放轨道 (Snapshot):</text>
        <text x="35" y="159" class="mono">event_bus.write_snapshot(event)</text>
        <text x="35" y="177" class="component-desc">节流写入 (如每10条)</text>
      </g>
    </g>

    <path class="flow-arrow" d="M 400 460 H 470"/>
    <text x="405" y="450" class="flow-text">写入</text>

    <!-- Infrastructure Module -->
    <g id="infra-layer">
      <rect x="470" y="290" width="300" height="340" fill="url(#infraGradient)" class="layer-box"/>
      <text x="500" y="338" class="layer-title">💾 基础设施: Redis</text>
      <g transform="translate(495, 380)">
        <rect width="250" height="100" class="component-box"/>
        <text x="125" y="30" text-anchor="middle" class="component-title">Pub/Sub Channel</text>
        <text x="125" y="55" text-anchor="middle" class="component-desc">用于实时消息传递</text>
        <text x="125" y="80" text-anchor="middle" class="mono">CHAT_EVENT:channel:{req_id}</text>
      </g>
      <g transform="translate(495, 510)">
        <rect width="250" height="100" class="component-box"/>
        <text x="125" y="30" text-anchor="middle" class="component-title">Snapshot Cache Key</text>
        <text x="125" y="55" text-anchor="middle" class="component-desc">用于回放和历史查询</text>
        <text x="125" y="80" text-anchor="middle" class="mono">CHAT_EVENT:{req_id} (TTL: 6h)</text>
      </g>
    </g>

    <path class="flow-arrow" d="M 770 460 H 840"/>
    <text x="775" y="450" class="flow-text">读取</text>

    <!-- Consumer Module -->
    <g id="consumer-layer">
      <rect x="840" y="200" width="350" height="520" fill="url(#consumerGradient)" class="layer-box"/>
      <text x="870" y="248" class="layer-title">📥 消费者模块</text>
      <text x="870" y="275" class="layer-desc">AsynchronousRequestProcessor</text>
      <g transform="translate(865, 320)">
        <rect width="300" height="330" class="component-box"/>
        <text x="150" y="35" text-anchor="middle" class="component-title">智能降级消费逻辑</text>

        <g transform="translate(20, 60)">
          <text class="component-desc" style="font-weight:600; fill:#1e40af">1. 优先路径: 订阅Pub/Sub</text>
          <text x="15" y="28" class="mono">stream = event_bus.subscribe()</text>
          <text x="15" y="46" class="mono">_stream_via_pubsub(stream)</text>
          <text x="15" y="70" class="component-desc">优点: 毫秒级延迟, 实时性好。</text>
          <text x="15" y="88" class="component-desc">处理: 基于`seq`的乱序缓冲, 保证有序。</text>
        </g>

        <path d="M 150 165 V 195" class="flow-arrow" style="stroke-dasharray: 4 4; stroke: #475569;"/>
        <text x="160" y="185" class="flow-text" style="font-size:12px;">若订阅异常</text>

        <g transform="translate(20, 200)">
          <text class="component-desc" style="font-weight:600; fill:#1e40af">2. 降级路径: 轮询快照</text>
          <text x="15" y="28" class="mono">_stream_from_redis()</text>
          <text x="15" y="46" class="mono">event_bus.read_snapshot()</text>
          <text x="15" y="70" class="component-desc">优点: 可靠性高, 保证最终可达。</text>
          <text x="15" y="88" class="component-desc">处理: 低频轮询, 仅在数据变化时产出。</text>
        </g>
      </g>
    </g>
  </g>
</svg>