<svg width="1920" height="1100" viewBox="0 0 1920 1100" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Color Palette -->
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="0%" y2="100%"><stop offset="0%" style="stop-color:#f8fafc"/><stop offset="100%" style="stop-color:#f1f5f9"/></linearGradient>
    <style>
      /* Typography */
      .title { font-family: 'Inter', -apple-system, sans-serif; font-size: 32px; font-weight: 700; fill: #1e293b; text-anchor: middle; }
      .subtitle { font-family: 'Inter', -apple-system, sans-serif; font-size: 16px; fill: #475569; text-anchor: middle; }
      .layer-title { font-family: 'Inter', -apple-system, sans-serif; font-size: 20px; font-weight: 600; fill: white; }
      .box-title { font-family: 'Inter', -apple-system, sans-serif; font-size: 16px; font-weight: 600; fill: #1e293b; }
      .text { font-family: 'Inter', -apple-system, sans-serif; font-size: 13px; fill: #475569; }
      .mono { font-family: 'SF Mono', 'Monaco', monospace; font-size: 12px; fill: #334155; }
      .data { font-weight: 600; fill: #059669; }
      .accent-text { fill: #b45309; font-weight: 600; }
      .flow-label { font-family: 'Inter', -apple-system, sans-serif; font-size: 13px; font-weight: 600; fill: #475569; text-anchor: middle; }

      /* Shapes &amp; Styles */
      .panel { fill: #ffffff; stroke: #e2e8f0; stroke-width: 1px; rx: 16; filter: url(#dropShadow); }
      .panel-header { fill: #64748b; border-top-left-radius: 16px; border-top-right-radius: 16px; }
      .input-header { fill: #3b82f6; }
      .queue-header { fill: #f59e0b; }
      .pipeline-header { fill: #10b981; }
      .output-header { fill: #8b5cf6; }

      .sub-panel { fill: #f8fafc; stroke: #e2e8f0; stroke-width: 1px; rx: 10; }
      .plugin-panel { fill: #ffffff; stroke-width: 1px; rx: 10; }

      /* Arrows &amp; Connectors */
      .arrow { stroke: #64748b; stroke-width: 2.5; fill: none; marker-end: url(#arrowhead); }
    </style>
    <!-- Filters &amp; Markers -->
    <filter id="dropShadow" x="-10%" y="-10%" width="120%" height="130%"><feDropShadow dx="0" dy="4" stdDeviation="8" flood-color="#1e293b" flood-opacity="0.07"/></filter>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto"><polygon points="0 0, 10 3.5, 0 7" fill="#64748b"/></marker>
  </defs>

  <!-- Background -->
  <rect width="1920" height="1100" fill="url(#bgGradient)"/>

  <!-- Main Title -->
  <text x="960" y="60" class="title">消息异步队列与事件处理流水线架构</text>
  <text x="960" y="90" class="subtitle">通过IO/CPU分离与分层确定性加工，构建高性能、高扩展的事件处理系统</text>

  <!-- Main Diagram Flow -->
  <g transform="translate(40, 150)">

    <!-- 1. Input Module -->
    <g id="input-module">
      <rect class="panel" width="380" height="520"/>
      <rect class="panel-header input-header" width="380" height="60" rx="16" ry="16"/>
      <text x="30" y="38" class="layer-title">📥 输入模块 (IO密集)</text>
      <g transform="translate(30, 80)">
        <rect class="sub-panel" width="320" height="120"/>
        <text x="160" y="30" text-anchor="middle" class="box-title">LangGraph 原始事件</text>
        <text x="160" y="55" text-anchor="middle" class="mono">(message_chunk, metadata)</text>
        <text x="160" y="80" text-anchor="middle" class="text accent-text">🌊 高频实时数据流</text>
        <text x="160" y="100" text-anchor="middle" class="text">AIMessageChunk, ToolMessage...</text>
      </g>
      <path d="M 190 200 v 40" class="arrow"/>
      <g transform="translate(30, 240)">
        <rect class="sub-panel" width="320" height="140"/>
        <text x="160" y="30" text-anchor="middle" class="box-title">ReasoningAgentEvent</text>
        <text x="160" y="55" text-anchor="middle" class="mono">.handle_message()</text>
        <text x="160" y="85" text-anchor="middle" class="text" style="font-weight:600">⚡ 快速入队策略:</text>
        <text x="160" y="105" text-anchor="middle" class="mono">queue.put_nowait((chunk, meta))</text>
        <text x="160" y="125" text-anchor="middle" class="text accent-text">🚀 非阻塞，毫秒级响应</text>
      </g>
    </g>

    <path d="M 380 340 h 60" class="arrow"/>
    <text class="flow-label" x="410" y="330">入队</text>

    <!-- 2. Queue -->
    <g id="queue-module" transform="translate(440, 250)">
      <rect class="panel" width="220" height="180"/>
      <rect class="panel-header queue-header" width="220" height="60" rx="16" ry="16"/>
      <text x="30" y="38" class="layer-title">🔗 异步队列</text>
      <g transform="translate(20, 80)">
        <text x="90" y="20" text-anchor="middle" class="box-title">asyncio.Queue</text>
        <text x="90" y="45" text-anchor="middle" class="text">核心解耦缓冲</text>
        <text x="90" y="70" text-anchor="middle" class="mono">await queue.get()</text>
        <text x="90" y="90" text-anchor="middle" class="text accent-text">串行消费保序</text>
      </g>
    </g>

    <path d="M 660 340 h 60" class="arrow"/>
    <text class="flow-label" x="690" y="330">串行消费</text>

    <!-- 3. Pipeline Module -->
    <g id="pipeline-module" transform="translate(720, 0)">
      <rect class="panel" width="1150" height="960"/>
      <rect class="panel-header pipeline-header" width="1150" height="60" rx="16" ry="16"/>
      <text x="30" y="38" class="layer-title">⚙️ 后台处理流水线 (CPU密集)</text>

      <g transform="translate(30, 80)">
        <!-- Stage 1: Collector -->
        <g id="collector-stage">
          <rect class="sub-panel" width="350" height="200"/>
          <text x="20" y="30" class="box-title">1. 收集器: MessageCollector</text>
          <text x="20" y="55" class="mono">.collect(repo, chunk, meta)</text>
          <text x="20" y="85" class="text" style="font-weight:600">核心功能:</text>
          <text x="35" y="110" class="text">• 解析原始数据 (Chunk/Tool)</text>
          <text x="35" y="130" class="text">• 提取 run_id, agent_name 等</text>
          <text x="35" y="150" class="text">• 关联上下文信息</text>
          <text x="175" y="180" text-anchor="middle" class="text" style="font-weight:600;">→ 归一化为 <tspan class="data">AgentMessage</tspan></text>
        </g>

        <path d="M 175 280 v 40" class="arrow"/>

        <!-- Stage 2: Repository -->
        <g id="repository-stage" transform="translate(0, 320)">
          <rect class="sub-panel" width="350" height="240"/>
          <text x="20" y="30" class="box-title">2. 仓库: MessageRepository</text>
          <text x="20" y="55" class="text" style="font-weight:600">内存中的单一事实源 (SSoT)</text>
          <text x="20" y="85" class="text" style="font-weight:600">高效索引 (O(1)):</text>
          <text x="35" y="115" class="mono data">• _by_run_id: Dict[str, ...]</text>
          <text x="35" y="140" class="mono data">• _by_agent: Dict[str, List]</text>
          <text x="35" y="165" class="mono data">• _by_tool_call_id: Dict[str, ...]</text>
          <text x="175" y="210" text-anchor="middle" class="text" style="font-weight:600;">为业务处理提供快速数据访问</text>
        </g>
      </g>

      <path d="M 380 340 h 60" class="arrow"/>
      <text class="flow-label" x="410" y="330">查询</text>

      <g transform="translate(440, 80)">
        <!-- Stage 3: Processor -->
        <g id="processor-stage">
          <rect class="sub-panel" width="680" height="850"/>
          <text x="20" y="30" class="box-title">3. 处理器: BusinessMessageProcessor</text>
          <text x="20" y="55" class="text" style="font-weight:600;">策略模式: 根据 agent_name 动态委托</text>

          <!-- Planner Plugin -->
          <g transform="translate(20, 85)">
            <rect class="plugin-panel" width="640" height="150" style="stroke:#3b82f6;"/>
            <text x="20" y="30" class="box-title" style="fill:#1d4ed8;">🎯 PlannerMessageProcessor</text>
            <text x="20" y="55" class="text">输入: repository.get_messages_by_agent("planner")</text>
            <text x="20" y="80" class="text">输出: <tspan class="data">{"thought": "...", "plan_steps": [...]}</tspan></text>
            <text x="20" y="115" class="text" style="font-weight:600;">业务价值: 提取结构化计划，用于前端展示。</text>
          </g>

          <!-- Researcher Plugin -->
          <g transform="translate(20, 255)">
            <rect class="plugin-panel" width="640" height="150" style="stroke:#16a34a;"/>
            <text x="20" y="30" class="box-title" style="fill:#15803d;">🔬 ResearcherMessageProcessor</text>
            <text x="20" y="55" class="text">输入: repository.get_messages_by_agent("researcher")</text>
            <text x="20" y="80" class="text">输出: <tspan class="data">{"executions": [...]}</tspan></text>
            <text x="20" y="115" class="text" style="font-weight:600;">业务价值: 解析工具调用和结果，聚合关键洞察。</text>
          </g>

          <!-- Extensibility Plugin -->
          <g transform="translate(20, 425)">
            <rect class="plugin-panel" width="640" height="160" style="stroke:#ca8a04;"/>
            <text x="20" y="30" class="box-title" style="fill:#a16207;">🔧 插件化扩展架构</text>
            <text x="20" y="60" class="text" style="font-weight:600;">接入新Agent只需3步:</text>
            <text x="35" y="85" class="text">1. 继承 <tspan class="mono">BaseMessageProcessor</tspan></text>
            <text x="35" y="105" class="text">2. 实现 <tspan class="mono">process_messages()</tspan> 和 <tspan class="mono">get_agent_name()</tspan></text>
            <text x="35" y="125" class="text">3. 在主处理器中注册新插件</text>
            <text x="340" y="145" text-anchor="middle" class="text accent-text">💡 零耦合设计，易扩展，高可测试性</text>
          </g>
        </g>
      </g>
      <path d="M 750 930 v 40" class="arrow"/>
    </g>

    <g transform="translate(720, 970)">
        <text class="flow-label" x="410" y="0">聚合业务数据</text>
        <path d="M 410 10 v 20" class="arrow"/>
        <rect class="sub-panel" x="235" y="30" width="350" height="80" style="stroke:#8b5cf6; stroke-width:1.5;"/>
        <text x="410" y="60" text-anchor="middle" class="box-title">4. 应用状态同步</text>
        <text x="410" y="85" text-anchor="middle" class="mono">_apply_event_data(processed_data)</text>
    </g>

    <path d="M 1000 1010 h 100" class="arrow"/>
    <text class="flow-label" x="1050" y="1000">版本变更</text>
  </g>

  <!-- 4. Output Module -->
  <g id="output-module" transform="translate(1100, 940)">
      <rect class="panel" width="380" height="140"/>
      <rect class="panel-header output-header" width="380" height="60" rx="16" ry="16"/>
      <text x="30" y="38" class="layer-title">📤 输出模块</text>
      <g transform="translate(30, 80)">
        <text x="160" y="15" text-anchor="middle" class="box-title">版本变更检测与推送</text>
        <text x="160" y="40" text-anchor="middle" class="text"><tspan class="mono">_version</tspan> 递增触发 <tspan class="mono">_version_changed</tspan> 事件</text>
        <text x="160" y="60" text-anchor="middle" class="text" style="font-weight:600; fill:#7c3aed;">yield 完整状态到客户端 📡</text>
      </g>
  </g>
</svg>